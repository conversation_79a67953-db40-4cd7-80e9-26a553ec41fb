# -*- coding: utf-8 -*-
# ------------------------------------------------------------------------------
# Name:         testExternal.py
# Purpose:      Controller for all tests employing external software and
#               human-confirmation. Uses TestExternal classes in modules.
#
# Authors: <AUTHORS>
#               <PERSON>
#
# Copyright:    Copyright © 2009 <PERSON>
# License:      BSD, see license.txt
# ------------------------------------------------------------------------------
from __future__ import annotations

from music21.test import testSingleCoreAll as test

# ------------------------------------------------------------------------------
if __name__ == '__main__':
    test.main('external')


