#!/usr/bin/env python3
"""
Simple test script to verify music21 installation and MuseScore integration.
"""

import music21

def test_basic_functionality():
    """Test basic music21 functionality."""
    print("Testing music21 basic functionality...")
    
    # Create a simple melody
    melody = music21.stream.Stream()
    
    # Add some notes
    melody.append(music21.note.Note("C4", quarterLength=1))
    melody.append(music21.note.Note("D4", quarterLength=1))
    melody.append(music21.note.Note("E4", quarterLength=1))
    melody.append(music21.note.Note("F4", quarterLength=1))
    melody.append(music21.note.Note("G4", quarterLength=2))
    
    # Add a time signature and key signature
    melody.insert(0, music21.meter.TimeSignature('4/4'))
    melody.insert(0, music21.key.KeySignature(0))  # C major
    
    print("✓ Created a simple melody")
    
    # Test MIDI export
    try:
        melody.write('midi', fp='test_melody.mid')
        print("✓ MIDI export successful")
    except Exception as e:
        print(f"✗ MIDI export failed: {e}")
    
    # Test MusicXML export
    try:
        melody.write('musicxml', fp='test_melody.xml')
        print("✓ MusicXML export successful")
    except Exception as e:
        print(f"✗ MusicXML export failed: {e}")
    
    return melody

def test_musescore_integration():
    """Test MuseScore integration."""
    print("\nTesting MuseScore integration...")
    
    try:
        # Check if MuseScore is configured
        env = music21.environment.Environment()
        musescore_path = env['musicxmlPath']
        print(f"✓ MuseScore path configured: {musescore_path}")
        
        # Note: We won't actually call show() here as it would open MuseScore
        print("✓ MuseScore integration ready (use .show() to open in MuseScore)")
        
    except Exception as e:
        print(f"✗ MuseScore integration issue: {e}")

def main():
    """Main test function."""
    print("Music21 Installation Test")
    print("=" * 40)
    print(f"Music21 version: {music21.VERSION_STR}")
    print()
    
    # Test basic functionality
    melody = test_basic_functionality()
    
    # Test MuseScore integration
    test_musescore_integration()
    
    print("\n" + "=" * 40)
    print("Test completed!")
    print("\nTo open the melody in MuseScore, run:")
    print("melody.show()  # This will open MuseScore with your melody")

if __name__ == "__main__":
    main()
