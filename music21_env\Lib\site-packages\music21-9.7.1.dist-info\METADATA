Metadata-Version: 2.4
Name: music21
Version: 9.7.1
Summary: A Toolkit for Computer-Aided Musical Analysis and Computational Musicology.
Project-URL: Download, https://github.com/cuthbertLab/music21/releases/
Project-URL: Homepage, https://github.com/cuthbertLab/music21
Project-URL: Documentation, https://www.music21.org/music21docs/
Author-email: <PERSON> <<EMAIL>>
License-Expression: BSD-3-Clause
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: OS Independent
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Artistic Software
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Multimedia :: Sound/Audio :: Conversion
Classifier: Topic :: Multimedia :: Sound/Audio :: MIDI
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Typing :: Typed
Requires-Python: >=3.10
Requires-Dist: chardet
Requires-Dist: joblib
Requires-Dist: jsonpickle
Requires-Dist: matplotlib
Requires-Dist: more-itertools
Requires-Dist: numpy>=1.26.4
Requires-Dist: requests
Requires-Dist: webcolors>=1.5
Description-Content-Type: text/markdown

# music21 #

`music21` -- A Toolkit for Computer-Aided Musical Analysis and 
Computational Musicology

Copyright © 2006-2025 [Michael Scott Asato Cuthbert](http://www.trecento.com)

For more information, visit:
https://www.music21.org/music21docs/

To try it out, visit:
https://tinyurl.com/m21colab (runs music21 v7)

And to install, see:
https://www.music21.org/music21docs/usersGuide/usersGuide_01_installing.html

`Music21` runs on Python 3.10+.  (Use version 4 on Python 2 or Py3.4, version 5
on Py3.5, version 6 on Py3.6, version 7 on Py3.7, version 8 on Py3.8/Py3.9.)

Released under the BSD (3-clause) license. See LICENSE.
Externally provided software (including the MIT-licensed Lilypond/MusicXML test Suite) and
music encoding in the corpus may have different licenses and/or copyrights. 
A no-corpus version of `music21` is available also on GitHub for those needing strict
BSD-license of all parts of the system.

[![Build Status](https://github.com/cuthbertLab/music21/workflows/maincheck/badge.svg)](https://github.com/cuthbertLab/music21)
[![Coverage Status](https://coveralls.io/repos/github/cuthbertLab/music21/badge.svg?branch=master)](https://coveralls.io/github/cuthbertLab/music21?branch=master)

## Documentation ##

[User's Guide](https://www.music21.org/music21docs/usersGuide/index.html)

[Module Documentation](https://www.music21.org/music21docs/moduleReference/index.html)

## Mailing list ##

See: https://groups.google.com/forum/#!forum/music21list

## Contributing Guide ##

[Contributing Guide](CONTRIBUTING.md)

## Community Code of Conduct<a name="community-code-of-conduct"></a> ##

`Music21` encourages contributions, discussions, and usage from all people interested in
music and computers. This encouragement extends to all people regardless of (among other aspects)
gender, race, sexual orientation, disability, religion, appearance, veteran status,
gender identity, socioeconomic status, or nationality.

Members of the community will strive to be friendly, patient, and welcoming, especially of
viewpoints and experiences different from our own. We reject harassment and contributions
(in mail, comments, or code) that belittle individuals or groups of people.

We ask all members of the community to be mindful particularly about assumptions of the
gender of users (including using correct pronouns in comments and code). We recognize that members
sometimes make mistakes and will, in general, accept sincere regrets for such cases.
Blatant or repeated violations of the code will result in the removal of the
contributor’s participation in the community.

The maintainers of `music21` and associated sites will commit themselves to enforcing
this code of conduct. Users who notice violations, including instances of abuse,
harassment, or otherwise unacceptable behavior are requested to contact 
<EMAIL>.
Maintainers will respect confidentiality with regard to reports.

## Acknowledgements ##

The early development of `music21` was supported by
the generosity of the Seaver Institute and the
National Endowment for the Humanities, along with MIT's Music and Theater Arts Section
and the School of Humanities, Arts, and Social Sciences.
